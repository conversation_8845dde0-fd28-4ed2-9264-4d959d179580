/* Reset dan Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-brand i {
    margin-right: 10px;
    color: #4ade80;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #4ade80;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    min-height: 400px;
}

.hero-image {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.doctors-showcase {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.doctor-img {
    width: 120px;
    height: 120px;
    border-radius: 15px;
    object-fit: cover;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
}

.doctor-img:hover {
    transform: translateY(-5px);
}

.hero-icon {
    font-size: 4rem;
    color: #2c5aa0;
    background: #fff;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    display: inline-block;
    margin-bottom: 1rem;
}

.carousel-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 1rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ccc;
    cursor: pointer;
    transition: background 0.3s ease;
}

.dot.active {
    background: #2c5aa0;
}

.hero-text {
    text-align: left;
}

.hero-text h1 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
    color: #1e3a8a;
    font-weight: 700;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #64748b;
    line-height: 1.6;
}

.cta-button {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(74, 222, 128, 0.3);
}

/* Excellence Center Section */
.excellence-center {
    padding: 80px 0;
    background: white;
}

.excellence-center h2 {
    text-align: center;
    font-size: 2.2rem;
    margin-bottom: 1rem;
    color: #333;
}

.subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 3rem;
    font-size: 1.1rem;
}

.excellence-carousel {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.carousel-btn {
    background: #2c5aa0;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: background 0.3s ease;
    margin: 0 20px;
}

.carousel-btn:hover {
    background: #1e3a8a;
}

.excellence-cards {
    display: flex;
    gap: 2rem;
    max-width: 800px;
}

.excellence-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex: 1;
    min-height: 250px;
}

.excellence-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.excellence-card i {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.excellence-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.excellence-card p {
    color: #666;
    line-height: 1.6;
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: background 0.3s ease;
}

.indicator.active {
    background: #2c5aa0;
}

/* Info Section */
.info-section {
    padding: 80px 0;
    background: white;
}

.info-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.info-content h2 {
    font-size: 2.2rem;
    color: #1e3a8a;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.info-content p {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.info-features {
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #334155;
}

.feature-item i {
    color: #4ade80;
    font-size: 1.1rem;
}

.info-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.info-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    max-width: 400px;
}

.info-card-small {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    padding: 2rem 1.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.info-card-small:hover {
    transform: translateY(-5px);
}

.info-card-small i {
    font-size: 2rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.info-card-small h4 {
    font-size: 1.8rem;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.info-card-small p {
    color: #64748b;
    font-size: 0.9rem;
    margin: 0;
}

/* Facilities and Services Section */
.facilities-services {
    padding: 80px 0;
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.facilities-services h2 {
    text-align: center;
    font-size: 2.2rem;
    margin-bottom: 3rem;
    color: #333;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.service-card i {
    font-size: 3rem;
    color: #4ade80;
    margin-bottom: 1rem;
}

.service-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.service-card p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #4ade80;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #4ade80;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Page-specific Styles */
.main-content {
    padding-top: 100px;
    min-height: calc(100vh - 200px);
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.page-header h1 i {
    color: #667eea;
    margin-right: 10px;
}

.page-header p {
    font-size: 1.2rem;
    color: #666;
}

/* Filter Section */
.filter-section {
    margin-bottom: 3rem;
}

.filter-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.filter-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.5rem;
}

.filter-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

/* Doctors Section */
.doctors-section h3 {
    margin-bottom: 2rem;
    color: #333;
    font-size: 1.8rem;
}

.doctors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.doctor-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.doctor-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.doctor-photo {
    text-align: center;
    margin-bottom: 1rem;
}

.doctor-photo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #667eea;
}

.doctor-info h4 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
    text-align: center;
}

.doctor-info p {
    margin-bottom: 0.5rem;
    color: #666;
    display: flex;
    align-items: center;
    gap: 8px;
}

.specialization {
    text-transform: capitalize;
    font-weight: 500;
}

.doctor-status {
    margin: 1rem 0;
    text-align: center;
}

.status-badge {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.available {
    background: #d4edda;
    color: #155724;
}

.status-badge.full {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.off {
    background: #e2e3e5;
    color: #383d41;
}

.no-doctors {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-doctors i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-doctors h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* Active navigation link */
.nav-menu a.active {
    color: #4ade80;
    font-weight: bold;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.category-icon {
    text-align: center;
    margin-bottom: 1.5rem;
}

.category-icon i {
    font-size: 3rem;
    color: #667eea;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 1.5rem;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.category-content h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: #333;
    text-align: center;
}

.category-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    text-align: center;
}

.category-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0.5rem;
    color: #555;
    font-size: 0.9rem;
}

.detail-item i {
    color: #667eea;
    width: 16px;
}

.category-card .btn {
    width: 100%;
    margin-top: 1rem;
}

/* Information Section */
.info-section {
    margin-top: 3rem;
}

.info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
}

.info-card h3 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.info-card h3 i {
    margin-right: 10px;
    color: #ff6b6b;
}

.info-card ul {
    list-style: none;
}

.info-card ul li {
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-card ul li i {
    color: #4ecdc4;
    font-size: 0.9rem;
}

/* Reservation Page Styles */
.reservation-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.reservation-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #667eea;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-label a {
    color: #667eea;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.reservation-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.reservation-info .info-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    color: #333;
}

.reservation-info .info-card h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.reservation-info .info-card h3 i {
    color: #667eea;
    margin-right: 10px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 1.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item i {
    color: #667eea;
    font-size: 1.2rem;
    margin-top: 2px;
    min-width: 20px;
}

.info-item strong {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

.info-item p {
    color: #666;
    margin: 0;
    line-height: 1.5;
}

.tips-card {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(78, 205, 196, 0.3);
}

.tips-card h3 {
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.tips-card h3 i {
    color: #fff;
    margin-right: 10px;
}

.tips-card ul {
    list-style: none;
}

.tips-card ul li {
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.tips-card ul li i {
    color: #fff;
    font-size: 0.9rem;
}

/* Receipt Page Styles */
.receipt-container {
    max-width: 800px;
    margin: 0 auto;
}

.receipt-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.receipt-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.clinic-info h2 {
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
}

.clinic-info p {
    margin: 0.2rem 0;
    opacity: 0.9;
}

.receipt-status .status-badge {
    background: #4ecdc4;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.receipt-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, #ddd, transparent);
    margin: 0 2rem;
}

.receipt-body {
    padding: 2rem;
}

.queue-number {
    text-align: center;
    margin-bottom: 2rem;
}

.queue-number h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.queue-display {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    padding: 1rem 2rem;
    border-radius: 15px;
    display: inline-block;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.patient-info,
.appointment-info,
.complaint-info,
.qr-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.patient-info:last-child,
.appointment-info:last-child,
.complaint-info:last-child,
.qr-section:last-child {
    border-bottom: none;
}

.patient-info h4,
.appointment-info h4,
.complaint-info h4,
.qr-section h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.patient-info h4 i,
.appointment-info h4 i,
.complaint-info h4 i,
.qr-section h4 i {
    color: #667eea;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item .label {
    font-weight: bold;
    color: #666;
    font-size: 0.9rem;
}

.info-item .value {
    color: #333;
    font-size: 1rem;
}

.complaint-info p {
    color: #333;
    line-height: 1.6;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.qr-container {
    text-align: center;
}

.qr-container canvas {
    border: 2px solid #ddd;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.qr-container p {
    color: #666;
    font-size: 0.9rem;
}

.receipt-footer {
    background: #f8f9fa;
    padding: 2rem;
}

.timestamp {
    margin-bottom: 1.5rem;
}

.timestamp p {
    color: #666;
    margin: 0;
}

.important-notes h5 {
    color: #e74c3c;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.important-notes ul {
    list-style: none;
    color: #666;
}

.important-notes ul li {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.important-notes ul li:before {
    content: "•";
    color: #e74c3c;
    position: absolute;
    left: 0;
}

.receipt-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.next-steps {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.next-steps h3 {
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
    font-size: 1.5rem;
}

.next-steps h3 i {
    color: #667eea;
    margin-right: 10px;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.step-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 10px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.step-number {
    background: #667eea;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin: 0 auto 1rem;
}

.step-card h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.step-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* WhatsApp Page Styles */
.whatsapp-container {
    max-width: 1000px;
    margin: 0 auto;
}

.whatsapp-hero {
    text-align: center;
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    margin-bottom: 3rem;
    box-shadow: 0 10px 30px rgba(37, 211, 102, 0.3);
}

.whatsapp-icon {
    margin-bottom: 1.5rem;
}

.whatsapp-icon i {
    font-size: 4rem;
    color: white;
}

.whatsapp-hero h2 {
    font-size: 2.2rem;
    margin-bottom: 1rem;
}

.whatsapp-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.services-section {
    margin-bottom: 3rem;
}

.services-section h3 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.service-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    border-color: #25d366;
}

.service-item i {
    font-size: 2.5rem;
    color: #25d366;
    margin-bottom: 1rem;
}

.service-item h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.service-item p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.quick-contact {
    margin-bottom: 3rem;
}

.quick-contact h3 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.contact-card.main-contact {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.contact-icon {
    background: rgba(255,255,255,0.2);
    padding: 1rem;
    border-radius: 50%;
    min-width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-card:not(.main-contact) .contact-icon {
    background: #f8f9fa;
}

.contact-icon i {
    font-size: 1.5rem;
    color: white;
}

.contact-card:not(.main-contact) .contact-icon i {
    color: #667eea;
}

.contact-info {
    flex: 1;
}

.contact-info h4 {
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.phone-number {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.3rem;
}

.availability {
    font-size: 0.9rem;
    opacity: 0.8;
}

.contact-card:not(.main-contact) .contact-info h4,
.contact-card:not(.main-contact) .phone-number,
.contact-card:not(.main-contact) .availability {
    color: #333;
}

.faq-section {
    margin-bottom: 3rem;
}

.faq-section h3 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.faq-list {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.faq-item {
    border-bottom: 1px solid #eee;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    padding: 1.5rem 2rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-question h4 {
    color: #333;
    margin: 0;
    font-size: 1.1rem;
}

.faq-question i {
    color: #667eea;
    transition: transform 0.3s ease;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer p {
    padding: 0 2rem 1.5rem;
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.tips-section h3 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.tips-section h3 i {
    color: #ffc107;
    margin-right: 10px;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.tip-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-3px);
}

.tip-card i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.tip-card h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.tip-card p {
    color: #666;
    line-height: 1.6;
    font-size: 0.9rem;
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .receipt-actions,
    .next-steps {
        display: none !important;
    }

    .main-content {
        padding-top: 0 !important;
    }

    .receipt-card {
        box-shadow: none !important;
        border: 1px solid #ddd;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-text {
        text-align: center;
        order: 2;
    }

    .hero-image {
        order: 1;
    }

    .doctors-showcase {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.5rem;
    }

    .doctor-img {
        width: 80px;
        height: 80px;
    }

    .info-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .info-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        max-width: 300px;
        margin: 0 auto;
    }

    .info-card-small {
        padding: 1.5rem 1rem;
    }

    .excellence-cards {
        flex-direction: column;
    }

    .carousel-btn {
        display: none;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .filter-form {
        grid-template-columns: 1fr;
    }

    .doctors-grid {
        grid-template-columns: 1fr;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .reservation-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .receipt-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .receipt-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .steps-grid {
        grid-template-columns: 1fr;
    }

    .contact-options {
        grid-template-columns: 1fr;
    }

    .contact-card {
        flex-direction: column;
        text-align: center;
    }

    .tips-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .whatsapp-hero {
        padding: 2rem 1rem;
    }

    .whatsapp-hero h2 {
        font-size: 1.8rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .queue-display {
        font-size: 2rem;
        padding: 0.8rem 1.5rem;
    }
}
