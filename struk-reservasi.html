<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk Reservasi - Healthy Care</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Healthy Care</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Beranda</a></li>
                <li><a href="ketersediaan-dokter.html">Dokter</a></li>
                <li><a href="kategori-layanan.html">Layanan</a></li>
                <li><a href="reservasi.html">Reservasi</a></li>
                <li><a href="whatsapp.html">WhatsApp</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-receipt"></i> Struk Reservasi</h1>
                <p>Bukti reservasi Anda telah berhasil dibuat</p>
            </div>

            <div class="receipt-container">
                <div class="receipt-card" id="receiptCard">
                    <div class="receipt-header">
                        <div class="clinic-info">
                            <h2><i class="fas fa-heartbeat"></i> Healthy Care</h2>
                            <p>Jl. Kesehatan No. 123, Jakarta Pusat</p>
                            <p>Telp: +**************</p>
                        </div>
                        <div class="receipt-status">
                            <span class="status-badge confirmed">TERKONFIRMASI</span>
                        </div>
                    </div>

                    <div class="receipt-divider"></div>

                    <div class="receipt-body">
                        <div class="queue-number">
                            <h3>Nomor Antrian</h3>
                            <div class="queue-display" id="queueNumber">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="patient-info">
                            <h4><i class="fas fa-user"></i> Data Pasien</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">Nama:</span>
                                    <span class="value" id="patientName">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Telepon:</span>
                                    <span class="value" id="patientPhone">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Jenis Kelamin:</span>
                                    <span class="value" id="patientGender">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Tanggal Lahir:</span>
                                    <span class="value" id="patientBirthDate">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="appointment-info">
                            <h4><i class="fas fa-calendar-alt"></i> Jadwal Kunjungan</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">Tanggal:</span>
                                    <span class="value" id="appointmentDate">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Waktu:</span>
                                    <span class="value" id="appointmentTime">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Layanan:</span>
                                    <span class="value" id="serviceCategory">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Dokter:</span>
                                    <span class="value" id="doctorName">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="complaint-info">
                            <h4><i class="fas fa-notes-medical"></i> Keluhan</h4>
                            <p id="patientComplaint">-</p>
                        </div>

                        <div class="qr-section">
                            <h4><i class="fas fa-qrcode"></i> QR Code</h4>
                            <div class="qr-container">
                                <canvas id="qrcode"></canvas>
                                <p>Tunjukkan QR Code ini saat kedatangan</p>
                            </div>
                        </div>
                    </div>

                    <div class="receipt-divider"></div>

                    <div class="receipt-footer">
                        <div class="timestamp">
                            <p><strong>Dibuat pada:</strong> <span id="createdAt">-</span></p>
                        </div>
                        <div class="important-notes">
                            <h5><i class="fas fa-exclamation-triangle"></i> Penting:</h5>
                            <ul>
                                <li>Harap datang 15 menit sebelum jadwal</li>
                                <li>Bawa kartu identitas dan struk ini</li>
                                <li>Untuk pembatalan, hubungi minimal 2 jam sebelumnya</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="receipt-actions">
                    <button class="btn btn-primary" onclick="printReceipt()">
                        <i class="fas fa-print"></i> Cetak Struk
                    </button>
                    <button class="btn btn-success" onclick="shareToWhatsApp()">
                        <i class="fab fa-whatsapp"></i> Bagikan ke WhatsApp
                    </button>
                    <button class="btn btn-secondary" onclick="downloadReceipt()">
                        <i class="fas fa-download"></i> Unduh PDF
                    </button>
                    <button class="btn btn-primary" onclick="location.href='index.html'">
                        <i class="fas fa-home"></i> Kembali ke Beranda
                    </button>
                </div>

                <div class="next-steps">
                    <h3><i class="fas fa-arrow-right"></i> Langkah Selanjutnya</h3>
                    <div class="steps-grid">
                        <div class="step-card">
                            <div class="step-number">1</div>
                            <h4>Simpan Struk</h4>
                            <p>Simpan atau cetak struk ini sebagai bukti reservasi</p>
                        </div>
                        <div class="step-card">
                            <div class="step-number">2</div>
                            <h4>Persiapan</h4>
                            <p>Siapkan dokumen yang diperlukan sebelum kedatangan</p>
                        </div>
                        <div class="step-card">
                            <div class="step-number">3</div>
                            <h4>Kedatangan</h4>
                            <p>Datang tepat waktu dan tunjukkan struk di resepsionis</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-heartbeat"></i> Healthy Care</h3>
                    <p>Layanan kesehatan digital terpercaya untuk kemudahan akses perawatan medis Anda.</p>
                </div>
                <div class="footer-section">
                    <h4>Layanan</h4>
                    <ul>
                        <li><a href="ketersediaan-dokter.html">Ketersediaan Dokter</a></li>
                        <li><a href="kategori-layanan.html">Kategori Layanan</a></li>
                        <li><a href="reservasi.html">Reservasi</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Kontak</h4>
                    <p><i class="fas fa-phone"></i> +**************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fab fa-whatsapp"></i> WhatsApp Business</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Healthy Care. Semua hak dilindungi.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        let reservationData = null;

        document.addEventListener('DOMContentLoaded', function() {
            loadReservationData();
            generateQRCode();
        });

        function loadReservationData() {
            reservationData = getFromLocalStorage('currentReservation');
            
            if (!reservationData) {
                showNotification('Data reservasi tidak ditemukan', 'error');
                setTimeout(() => {
                    window.location.href = 'reservasi.html';
                }, 2000);
                return;
            }

            populateReceiptData();
        }

        function populateReceiptData() {
            // Queue number
            document.getElementById('queueNumber').textContent = reservationData.queueNumber;

            // Patient info
            document.getElementById('patientName').textContent = reservationData.namaLengkap;
            document.getElementById('patientPhone').textContent = reservationData.nomorTelepon;
            document.getElementById('patientGender').textContent = 
                reservationData.jenisKelamin === 'laki-laki' ? 'Laki-laki' : 'Perempuan';
            document.getElementById('patientBirthDate').textContent = reservationData.tanggalLahirFormatted;

            // Appointment info
            document.getElementById('appointmentDate').textContent = reservationData.tanggalKunjunganFormatted;
            document.getElementById('appointmentTime').textContent = getTimeSlotText(reservationData.waktuKunjungan);
            document.getElementById('serviceCategory').textContent = reservationData.categoryName;
            document.getElementById('doctorName').textContent = reservationData.doctorName;

            // Complaint
            document.getElementById('patientComplaint').textContent = reservationData.keluhan;

            // Timestamp
            const createdDate = new Date(reservationData.timestamp);
            document.getElementById('createdAt').textContent = 
                createdDate.toLocaleDateString('id-ID') + ' ' + createdDate.toLocaleTimeString('id-ID');
        }

        function getTimeSlotText(timeSlot) {
            const timeMap = {
                'pagi': 'Pagi (08:00 - 12:00)',
                'siang': 'Siang (12:00 - 16:00)',
                'sore': 'Sore (16:00 - 20:00)',
                'malam': 'Malam (20:00 - 24:00)'
            };
            return timeMap[timeSlot] || timeSlot;
        }

        function generateQRCode() {
            if (!reservationData) return;

            const qrData = JSON.stringify({
                queueNumber: reservationData.queueNumber,
                patientName: reservationData.namaLengkap,
                appointmentDate: reservationData.tanggalKunjungan,
                doctorName: reservationData.doctorName
            });

            const canvas = document.getElementById('qrcode');
            QRCode.toCanvas(canvas, qrData, {
                width: 150,
                height: 150,
                colorDark: '#333',
                colorLight: '#fff'
            });
        }

        function printReceipt() {
            window.print();
        }

        function shareToWhatsApp() {
            if (!reservationData) return;

            const message = `*STRUK RESERVASI HEALTHY CARE*

*Nomor Antrian:* ${reservationData.queueNumber}

*Data Pasien:*
Nama: ${reservationData.namaLengkap}
Telepon: ${reservationData.nomorTelepon}

*Jadwal Kunjungan:*
Tanggal: ${reservationData.tanggalKunjunganFormatted}
Waktu: ${getTimeSlotText(reservationData.waktuKunjungan)}
Layanan: ${reservationData.categoryName}
Dokter: ${reservationData.doctorName}

*Keluhan:* ${reservationData.keluhan}

Harap datang 15 menit sebelum jadwal. Terima kasih!`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        function downloadReceipt() {
            // Simple implementation - in real app, you'd use a PDF library
            showNotification('Fitur unduh PDF akan segera tersedia', 'info');
        }
    </script>
</body>
</html>
