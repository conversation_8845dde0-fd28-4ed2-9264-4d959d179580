// Doctor data for each category
const doctorsByCategory = {
    umum: [
        { id: 1, name: "Dr. <PERSON>, Sp.PD" },
        { id: 2, name: "Dr. <PERSON><PERSON>, M.<PERSON>" },
        { id: 3, name: "Dr<PERSON>, Sp.PD" }
    ],
    anak: [
        { id: 4, name: "Dr. <PERSON>, Sp.<PERSON>" },
        { id: 5, name: "Dr. <PERSON>, Sp.A" },
        { id: 6, name: "Dr. <PERSON><PERSON>, Sp.O<PERSON>" }
    ],
    imunisasi: [
        { id: 7, name: "<PERSON><PERSON><PERSON>" },
        { id: 8, name: "Dr<PERSON> <PERSON><PERSON>, M.<PERSON>" }
    ],
    gizi: [
        { id: 9, name: "<PERSON><PERSON>" },
        { id: 10, name: "<PERSON><PERSON>" }
    ],
    gigi: [
        { id: 11, name: "Dr. <PERSON>, drg" },
        { id: 12, name: "Dr. <PERSON><PERSON>, drg" }
    ],
    mata: [
        { id: 13, name: "Dr<PERSON> <PERSON>, Sp.<PERSON>" },
        { id: 14, name: "<PERSON>. <PERSON>, <PERSON><PERSON><PERSON>" }
    ],
    tht: [
        { id: 15, name: "<PERSON><PERSON>, <PERSON>p.<PERSON>" },
        { id: 16, name: "Dr. <PERSON>na <PERSON>wi, Sp.THT" }
    ],
    kulit: [
        { id: 17, name: "Dr. Indah Sari, Sp.KK" },
        { id: 18, name: "Dr. Bambang Sutrisno, Sp.KK" }
    ]
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum dates
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('tanggalLahir').max = today;
    document.getElementById('tanggalKunjungan').min = today;
    
    // Set default visit date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    document.getElementById('tanggalKunjungan').value = tomorrow.toISOString().split('T')[0];

    // Check URL parameters
    checkURLParameters();

    // Add form submission handler
    document.getElementById('reservationForm').addEventListener('submit', handleFormSubmission);
});

function checkURLParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const doctor = urlParams.get('doctor');
    const date = urlParams.get('date');
    const category = urlParams.get('category');

    if (date) {
        document.getElementById('tanggalKunjungan').value = date;
    }

    if (category) {
        document.getElementById('kategoriLayanan').value = category;
        updateDoctorList();
    }

    if (doctor) {
        // Wait for doctor list to be updated
        setTimeout(() => {
            const doctorSelect = document.getElementById('pilihanDokter');
            for (let option of doctorSelect.options) {
                if (option.text === doctor) {
                    option.selected = true;
                    break;
                }
            }
        }, 100);
    }
}

function updateDoctorList() {
    const categorySelect = document.getElementById('kategoriLayanan');
    const doctorSelect = document.getElementById('pilihanDokter');
    const selectedCategory = categorySelect.value;

    // Clear existing options
    doctorSelect.innerHTML = '<option value="">Pilih dokter</option>';

    if (selectedCategory && doctorsByCategory[selectedCategory]) {
        const doctors = doctorsByCategory[selectedCategory];
        doctors.forEach(doctor => {
            const option = document.createElement('option');
            option.value = doctor.id;
            option.textContent = doctor.name;
            doctorSelect.appendChild(option);
        });
        doctorSelect.disabled = false;
    } else {
        doctorSelect.innerHTML = '<option value="">Pilih kategori layanan terlebih dahulu</option>';
        doctorSelect.disabled = true;
    }
}

function handleFormSubmission(event) {
    event.preventDefault();

    // Validate form
    if (!validateReservationForm()) {
        return;
    }

    // Collect form data
    const formData = collectFormData();

    // Generate queue number
    const queueNumber = generateQueueNumber();

    // Create reservation object
    const reservation = {
        ...formData,
        queueNumber: queueNumber,
        timestamp: new Date().toISOString(),
        status: 'confirmed'
    };

    // Save to localStorage
    saveToLocalStorage('currentReservation', reservation);

    // Show success message
    showNotification('Reservasi berhasil dibuat! Anda akan diarahkan ke halaman struk.', 'success');

    // Redirect to receipt page
    setTimeout(() => {
        window.location.href = 'struk-reservasi.html';
    }, 2000);
}

function validateReservationForm() {
    const form = document.getElementById('reservationForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    // Check required fields
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });

    // Validate phone number
    const phone = document.getElementById('nomorTelepon').value;
    if (phone && !validatePhone(phone)) {
        document.getElementById('nomorTelepon').classList.add('error');
        showNotification('Format nomor telepon tidak valid', 'error');
        isValid = false;
    }

    // Validate email if provided
    const email = document.getElementById('email').value;
    if (email && !validateEmail(email)) {
        document.getElementById('email').classList.add('error');
        showNotification('Format email tidak valid', 'error');
        isValid = false;
    }

    // Validate visit date (must be future date)
    const visitDate = new Date(document.getElementById('tanggalKunjungan').value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (visitDate < today) {
        document.getElementById('tanggalKunjungan').classList.add('error');
        showNotification('Tanggal kunjungan harus hari ini atau setelahnya', 'error');
        isValid = false;
    }

    // Validate birth date (must be past date)
    const birthDate = new Date(document.getElementById('tanggalLahir').value);
    if (birthDate >= today) {
        document.getElementById('tanggalLahir').classList.add('error');
        showNotification('Tanggal lahir tidak valid', 'error');
        isValid = false;
    }

    if (!isValid) {
        showNotification('Mohon lengkapi semua field yang wajib diisi dengan benar', 'error');
    }

    return isValid;
}

function collectFormData() {
    const form = document.getElementById('reservationForm');
    const formData = new FormData(form);
    const data = {};

    // Convert FormData to object
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // Get selected doctor name
    const doctorSelect = document.getElementById('pilihanDokter');
    data.doctorName = doctorSelect.options[doctorSelect.selectedIndex]?.text || 'Tidak dipilih';

    // Get selected category name
    const categorySelect = document.getElementById('kategoriLayanan');
    data.categoryName = categorySelect.options[categorySelect.selectedIndex]?.text || '';

    // Format dates
    data.tanggalKunjunganFormatted = formatDateIndonesian(data.tanggalKunjungan);
    data.tanggalLahirFormatted = formatDateIndonesian(data.tanggalLahir);

    return data;
}

function resetForm() {
    if (confirm('Apakah Anda yakin ingin mengosongkan semua field?')) {
        document.getElementById('reservationForm').reset();
        
        // Reset doctor list
        const doctorSelect = document.getElementById('pilihanDokter');
        doctorSelect.innerHTML = '<option value="">Pilih kategori layanan terlebih dahulu</option>';
        doctorSelect.disabled = true;

        // Remove error classes
        document.querySelectorAll('.error').forEach(element => {
            element.classList.remove('error');
        });

        // Reset dates
        const today = new Date().toISOString().split('T')[0];
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('tanggalKunjungan').value = tomorrow.toISOString().split('T')[0];

        showNotification('Form telah direset', 'success');
    }
}

function showTerms() {
    const termsContent = `
        <div style="max-height: 400px; overflow-y: auto; padding: 20px; line-height: 1.6;">
            <h3>Syarat dan Ketentuan Reservasi Healthy Care</h3>
            
            <h4>1. Ketentuan Umum</h4>
            <ul>
                <li>Reservasi hanya dapat dibuat untuk tanggal hari ini atau setelahnya</li>
                <li>Pasien wajib datang 15 menit sebelum jadwal yang telah ditentukan</li>
                <li>Keterlambatan lebih dari 30 menit dapat mengakibatkan pembatalan otomatis</li>
            </ul>

            <h4>2. Pembatalan dan Perubahan</h4>
            <ul>
                <li>Pembatalan atau perubahan jadwal harus dilakukan minimal 2 jam sebelum waktu kunjungan</li>
                <li>Pembatalan dapat dilakukan melalui WhatsApp atau telepon</li>
                <li>Tidak ada biaya pembatalan untuk reservasi yang dibatalkan sesuai ketentuan</li>
            </ul>

            <h4>3. Dokumen yang Diperlukan</h4>
            <ul>
                <li>Kartu identitas (KTP/SIM/Paspor)</li>
                <li>Kartu BPJS (jika menggunakan BPJS)</li>
                <li>Struk reservasi (digital atau cetak)</li>
            </ul>

            <h4>4. Privasi dan Keamanan Data</h4>
            <ul>
                <li>Data pribadi akan dijaga kerahasiaannya sesuai peraturan yang berlaku</li>
                <li>Informasi medis hanya akan diakses oleh tenaga medis yang berwenang</li>
            </ul>

            <h4>5. Biaya dan Pembayaran</h4>
            <ul>
                <li>Biaya konsultasi sesuai dengan tarif yang berlaku</li>
                <li>Pembayaran dapat dilakukan tunai atau transfer</li>
                <li>Untuk pengguna BPJS, harap membawa kartu yang masih aktif</li>
            </ul>
        </div>
    `;

    // Create modal
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        max-width: 600px;
        width: 90%;
        border-radius: 10px;
        position: relative;
    `;

    modalContent.innerHTML = termsContent + `
        <div style="padding: 20px; text-align: center; border-top: 1px solid #eee;">
            <button onclick="this.closest('.modal').remove()" 
                    style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                Tutup
            </button>
        </div>
    `;

    modal.className = 'modal';
    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // Close on background click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}
