<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ketersediaan Dokter - Healthy Care</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Healthy Care</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Beranda</a></li>
                <li><a href="ketersediaan-dokter.html" class="active">Dokter</a></li>
                <li><a href="kategori-layanan.html">Layanan</a></li>
                <li><a href="reservasi.html">Reservasi</a></li>
                <li><a href="whatsapp.html">WhatsApp</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-user-md"></i> Ketersediaan Dokter</h1>
                <p>Cek jadwal dan ketersediaan dokter berdasarkan tanggal dan spesialisasi</p>
            </div>

            <!-- Filter Section -->
            <div class="filter-section">
                <div class="filter-card">
                    <h3>Filter Pencarian</h3>
                    <div class="filter-form">
                        <div class="form-group">
                            <label for="tanggal">Pilih Tanggal:</label>
                            <input type="date" id="tanggal" name="tanggal" min="" required>
                        </div>
                        <div class="form-group">
                            <label for="spesialisasi">Spesialisasi:</label>
                            <select id="spesialisasi" name="spesialisasi">
                                <option value="">Semua Spesialisasi</option>
                                <option value="umum">Dokter Umum</option>
                                <option value="anak">Dokter Anak</option>
                                <option value="kandungan">Dokter Kandungan</option>
                                <option value="gigi">Dokter Gigi</option>
                                <option value="mata">Dokter Mata</option>
                                <option value="tht">Dokter THT</option>
                                <option value="kulit">Dokter Kulit</option>
                                <option value="jantung">Dokter Jantung</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="waktu">Waktu Praktik:</label>
                            <select id="waktu" name="waktu">
                                <option value="">Semua Waktu</option>
                                <option value="pagi">Pagi (08:00 - 12:00)</option>
                                <option value="siang">Siang (12:00 - 16:00)</option>
                                <option value="sore">Sore (16:00 - 20:00)</option>
                                <option value="malam">Malam (20:00 - 24:00)</option>
                            </select>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="filterDoctors()">
                            <i class="fas fa-search"></i> Cari Dokter
                        </button>
                    </div>
                </div>
            </div>

            <!-- Doctors List -->
            <div class="doctors-section">
                <h3>Daftar Dokter Tersedia</h3>
                <div id="doctors-list" class="doctors-grid">
                    <!-- Doctor cards will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-heartbeat"></i> Healthy Care</h3>
                    <p>Layanan kesehatan digital terpercaya untuk kemudahan akses perawatan medis Anda.</p>
                </div>
                <div class="footer-section">
                    <h4>Layanan</h4>
                    <ul>
                        <li><a href="ketersediaan-dokter.html">Ketersediaan Dokter</a></li>
                        <li><a href="kategori-layanan.html">Kategori Layanan</a></li>
                        <li><a href="reservasi.html">Reservasi</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Kontak</h4>
                    <p><i class="fas fa-phone"></i> +**************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fab fa-whatsapp"></i> WhatsApp Business</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Healthy Care. Semua hak dilindungi.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Sample doctor data
        const doctorsData = [
            {
                id: 1,
                nama: "Dr. Ahmad Wijaya, Sp.PD",
                spesialisasi: "umum",
                foto: "https://via.placeholder.com/150x150/667eea/ffffff?text=Dr.A",
                jadwal: {
                    senin: ["pagi", "siang"],
                    selasa: ["pagi", "sore"],
                    rabu: ["siang", "sore"],
                    kamis: ["pagi", "siang"],
                    jumat: ["pagi"],
                    sabtu: ["pagi", "siang"],
                    minggu: []
                },
                status: "tersedia"
            },
            {
                id: 2,
                nama: "Dr. Siti Nurhaliza, Sp.A",
                spesialisasi: "anak",
                foto: "https://via.placeholder.com/150x150/ff6b6b/ffffff?text=Dr.S",
                jadwal: {
                    senin: ["siang", "sore"],
                    selasa: ["pagi", "siang"],
                    rabu: ["pagi", "sore"],
                    kamis: ["siang", "sore"],
                    jumat: ["pagi", "siang"],
                    sabtu: ["pagi"],
                    minggu: []
                },
                status: "tersedia"
            },
            {
                id: 3,
                nama: "Dr. Budi Santoso, Sp.OG",
                spesialisasi: "kandungan",
                foto: "https://via.placeholder.com/150x150/4ecdc4/ffffff?text=Dr.B",
                jadwal: {
                    senin: ["pagi", "siang"],
                    selasa: ["sore", "malam"],
                    rabu: ["pagi", "siang"],
                    kamis: ["sore", "malam"],
                    jumat: ["pagi", "siang"],
                    sabtu: [],
                    minggu: []
                },
                status: "penuh"
            },
            {
                id: 4,
                nama: "Dr. Maya Sari, drg",
                spesialisasi: "gigi",
                foto: "https://via.placeholder.com/150x150/45b7d1/ffffff?text=Dr.M",
                jadwal: {
                    senin: ["siang", "sore"],
                    selasa: ["pagi", "siang"],
                    rabu: ["sore", "malam"],
                    kamis: ["pagi", "siang"],
                    jumat: ["siang", "sore"],
                    sabtu: ["pagi", "siang"],
                    minggu: []
                },
                status: "tersedia"
            }
        ];

        // Set minimum date to today
        document.getElementById('tanggal').min = new Date().toISOString().split('T')[0];
        document.getElementById('tanggal').value = new Date().toISOString().split('T')[0];

        function getDayName(date) {
            const days = ['minggu', 'senin', 'selasa', 'rabu', 'kamis', 'jumat', 'sabtu'];
            return days[new Date(date).getDay()];
        }

        function getStatusBadge(status) {
            const badges = {
                'tersedia': '<span class="status-badge available">Tersedia</span>',
                'penuh': '<span class="status-badge full">Penuh</span>',
                'libur': '<span class="status-badge off">Libur</span>'
            };
            return badges[status] || badges['libur'];
        }

        function getTimeSlots(slots) {
            const timeMap = {
                'pagi': '08:00 - 12:00',
                'siang': '12:00 - 16:00',
                'sore': '16:00 - 20:00',
                'malam': '20:00 - 24:00'
            };
            return slots.map(slot => timeMap[slot]).join(', ');
        }

        function filterDoctors() {
            const tanggal = document.getElementById('tanggal').value;
            const spesialisasi = document.getElementById('spesialisasi').value;
            const waktu = document.getElementById('waktu').value;

            if (!tanggal) {
                showNotification('Silakan pilih tanggal terlebih dahulu', 'error');
                return;
            }

            const dayName = getDayName(tanggal);
            let filteredDoctors = doctorsData.filter(doctor => {
                // Filter by specialization
                if (spesialisasi && doctor.spesialisasi !== spesialisasi) {
                    return false;
                }

                // Check if doctor works on selected day
                const daySchedule = doctor.jadwal[dayName];
                if (!daySchedule || daySchedule.length === 0) {
                    return false;
                }

                // Filter by time if specified
                if (waktu && !daySchedule.includes(waktu)) {
                    return false;
                }

                return true;
            });

            displayDoctors(filteredDoctors, dayName);
        }

        function displayDoctors(doctors, dayName) {
            const doctorsList = document.getElementById('doctors-list');
            
            if (doctors.length === 0) {
                doctorsList.innerHTML = `
                    <div class="no-doctors">
                        <i class="fas fa-user-md-slash"></i>
                        <h3>Tidak ada dokter tersedia</h3>
                        <p>Silakan coba dengan filter yang berbeda atau pilih tanggal lain.</p>
                    </div>
                `;
                return;
            }

            doctorsList.innerHTML = doctors.map(doctor => {
                const daySchedule = doctor.jadwal[dayName] || [];
                const timeSlots = getTimeSlots(daySchedule);
                
                return `
                    <div class="doctor-card">
                        <div class="doctor-photo">
                            <img src="${doctor.foto}" alt="${doctor.nama}">
                        </div>
                        <div class="doctor-info">
                            <h4>${doctor.nama}</h4>
                            <p class="specialization">
                                <i class="fas fa-stethoscope"></i>
                                ${doctor.spesialisasi.charAt(0).toUpperCase() + doctor.spesialisasi.slice(1)}
                            </p>
                            <p class="schedule">
                                <i class="fas fa-clock"></i>
                                ${timeSlots}
                            </p>
                            <div class="doctor-status">
                                ${getStatusBadge(doctor.status)}
                            </div>
                            <button class="btn btn-primary" 
                                    onclick="selectDoctor(${doctor.id}, '${doctor.nama}')"
                                    ${doctor.status !== 'tersedia' ? 'disabled' : ''}>
                                <i class="fas fa-calendar-plus"></i>
                                Buat Reservasi
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function selectDoctor(doctorId, doctorName) {
            const tanggal = document.getElementById('tanggal').value;
            const url = `reservasi.html?doctor=${encodeURIComponent(doctorName)}&date=${tanggal}`;
            window.location.href = url;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            filterDoctors(); // Show all available doctors for today
        });
    </script>
</body>
</html>
