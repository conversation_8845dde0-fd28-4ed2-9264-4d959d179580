<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Business - Healthy Care</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Healthy Care</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Beranda</a></li>
                <li><a href="ketersediaan-dokter.html">Dokter</a></li>
                <li><a href="kategori-layanan.html">Layanan</a></li>
                <li><a href="reservasi.html">Reservasi</a></li>
                <li><a href="whatsapp.html" class="active">WhatsApp</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h1><i class="fab fa-whatsapp"></i> WhatsApp Business</h1>
                <p>Hubungi kami langsung melalui WhatsApp untuk bantuan dan konsultasi</p>
            </div>

            <div class="whatsapp-container">
                <div class="whatsapp-hero">
                    <div class="whatsapp-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <h2>Layanan WhatsApp 24/7</h2>
                    <p>Tim customer service kami siap membantu Anda kapan saja melalui WhatsApp Business</p>
                </div>

                <div class="services-section">
                    <h3>Layanan yang Tersedia</h3>
                    <div class="services-grid">
                        <div class="service-item" onclick="sendWhatsAppMessage('bantuan')">
                            <i class="fas fa-question-circle"></i>
                            <h4>Bantuan Umum</h4>
                            <p>Pertanyaan seputar layanan dan cara penggunaan aplikasi</p>
                            <button class="btn btn-success">
                                <i class="fab fa-whatsapp"></i> Chat Sekarang
                            </button>
                        </div>

                        <div class="service-item" onclick="sendWhatsAppMessage('reservasi')">
                            <i class="fas fa-calendar-alt"></i>
                            <h4>Bantuan Reservasi</h4>
                            <p>Bantuan membuat, mengubah, atau membatalkan reservasi</p>
                            <button class="btn btn-success">
                                <i class="fab fa-whatsapp"></i> Chat Sekarang
                            </button>
                        </div>

                        <div class="service-item" onclick="sendWhatsAppMessage('konsultasi')">
                            <i class="fas fa-comments"></i>
                            <h4>Konsultasi Singkat</h4>
                            <p>Konsultasi awal dan tanya jawab seputar kesehatan</p>
                            <button class="btn btn-success">
                                <i class="fab fa-whatsapp"></i> Chat Sekarang
                            </button>
                        </div>

                        <div class="service-item" onclick="sendWhatsAppMessage('darurat')">
                            <i class="fas fa-ambulance"></i>
                            <h4>Layanan Darurat</h4>
                            <p>Bantuan untuk kondisi medis yang memerlukan penanganan segera</p>
                            <button class="btn btn-success">
                                <i class="fab fa-whatsapp"></i> Chat Sekarang
                            </button>
                        </div>
                    </div>
                </div>

                <div class="quick-contact">
                    <h3>Kontak Langsung</h3>
                    <div class="contact-options">
                        <div class="contact-card main-contact">
                            <div class="contact-icon">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="contact-info">
                                <h4>WhatsApp Business</h4>
                                <p class="phone-number">+62 812 3456 7890</p>
                                <p class="availability">Online 24/7</p>
                            </div>
                            <button class="btn btn-success" onclick="openWhatsApp()">
                                <i class="fab fa-whatsapp"></i> Chat Langsung
                            </button>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-info">
                                <h4>Telepon</h4>
                                <p class="phone-number">+**************</p>
                                <p class="availability">Senin - Minggu: 08:00 - 20:00</p>
                            </div>
                            <button class="btn btn-secondary" onclick="callPhone()">
                                <i class="fas fa-phone"></i> Telepon
                            </button>
                        </div>
                    </div>
                </div>

                <div class="faq-section">
                    <h3>Pertanyaan yang Sering Diajukan</h3>
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <h4>Bagaimana cara membuat reservasi melalui WhatsApp?</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Anda dapat mengirim pesan ke WhatsApp Business kami dengan format: "Reservasi - Nama - Tanggal - Layanan". Tim kami akan membantu proses reservasi Anda.</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <h4>Apakah konsultasi melalui WhatsApp berbayar?</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Konsultasi awal dan informasi umum melalui WhatsApp adalah gratis. Untuk konsultasi medis yang lebih mendalam, akan ada biaya sesuai tarif yang berlaku.</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <h4>Berapa lama waktu respons WhatsApp?</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Tim customer service kami berusaha merespons dalam waktu maksimal 15 menit selama jam operasional. Untuk di luar jam operasional, respons akan diberikan pada hari kerja berikutnya.</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <h4>Bisakah saya membatalkan reservasi melalui WhatsApp?</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Ya, Anda dapat membatalkan atau mengubah reservasi melalui WhatsApp dengan menyertakan nomor antrian. Pembatalan harus dilakukan minimal 2 jam sebelum jadwal.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tips-section">
                    <h3><i class="fas fa-lightbulb"></i> Tips Menggunakan WhatsApp Business</h3>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <i class="fas fa-user"></i>
                            <h4>Perkenalkan Diri</h4>
                            <p>Mulai percakapan dengan menyebutkan nama dan keperluan Anda</p>
                        </div>
                        <div class="tip-card">
                            <i class="fas fa-clock"></i>
                            <h4>Sabar Menunggu</h4>
                            <p>Tim kami akan merespons secepat mungkin, mohon bersabar</p>
                        </div>
                        <div class="tip-card">
                            <i class="fas fa-info"></i>
                            <h4>Berikan Detail</h4>
                            <p>Semakin detail informasi yang Anda berikan, semakin baik bantuan yang kami berikan</p>
                        </div>
                        <div class="tip-card">
                            <i class="fas fa-shield-alt"></i>
                            <h4>Jaga Privasi</h4>
                            <p>Hindari membagikan informasi sensitif seperti password atau data pribadi</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-heartbeat"></i> Healthy Care</h3>
                    <p>Layanan kesehatan digital terpercaya untuk kemudahan akses perawatan medis Anda.</p>
                </div>
                <div class="footer-section">
                    <h4>Layanan</h4>
                    <ul>
                        <li><a href="ketersediaan-dokter.html">Ketersediaan Dokter</a></li>
                        <li><a href="kategori-layanan.html">Kategori Layanan</a></li>
                        <li><a href="reservasi.html">Reservasi</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Kontak</h4>
                    <p><i class="fas fa-phone"></i> +**************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fab fa-whatsapp"></i> WhatsApp Business</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Healthy Care. Semua hak dilindungi.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        const whatsappNumber = "6281234567890"; // Replace with actual WhatsApp Business number

        function sendWhatsAppMessage(type) {
            let message = "";
            
            switch(type) {
                case 'bantuan':
                    message = "Halo, saya membutuhkan bantuan umum terkait layanan Healthy Care.";
                    break;
                case 'reservasi':
                    message = "Halo, saya membutuhkan bantuan untuk membuat/mengubah reservasi.";
                    break;
                case 'konsultasi':
                    message = "Halo, saya ingin melakukan konsultasi singkat.";
                    break;
                case 'darurat':
                    message = "Halo, saya membutuhkan bantuan untuk kondisi medis darurat.";
                    break;
                default:
                    message = "Halo, saya membutuhkan bantuan.";
            }
            
            const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        function openWhatsApp() {
            const message = "Halo Healthy Care, saya ingin mendapatkan informasi lebih lanjut.";
            const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        function callPhone() {
            window.open('tel:+**********', '_self');
        }

        function toggleFAQ(element) {
            const faqItem = element.parentElement;
            const answer = faqItem.querySelector('.faq-answer');
            const icon = element.querySelector('i');
            
            faqItem.classList.toggle('active');
            
            if (faqItem.classList.contains('active')) {
                answer.style.maxHeight = answer.scrollHeight + 'px';
                icon.style.transform = 'rotate(180deg)';
            } else {
                answer.style.maxHeight = '0';
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>
</body>
</html>
